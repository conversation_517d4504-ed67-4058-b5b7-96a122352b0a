% Created: 2023-09-17
% Author:  <PERSON>

\documentclass[12pt, a4paper]{article}

\usepackage[margin=0.4in, paperwidth=98.666667mm, paperheight=210mm]{geometry}  % paper size :: a4third
%% \usepackage[margin=0.51in, paperwidth=3.666667in, paperheight=8.5in]{geometry}  % paper size :: letterthird
\usepackage[
  width=21.0truecm, height=29.7truecm, % set width and height to a4
  % use any combination of these options to add different cut markings
  cam, axes, frame, cross,
  pdftex,   % set the type of TeX renderer you use
  center   % center the contents
]{crop}
\usepackage{hyperref}
\usepackage[scaled]{helvet}
\renewcommand*\familydefault{\sfdefault}
\usepackage[T1]{fontenc}
\usepackage{fontspec}
\usepackage{tikz}
\hypersetup{
  pdftitle={Ariadne Checklists},
  pdfsubject={Autopilot},
  pdfauthor={<PERSON>},
  pdfkeywords={autopilot, checklist, linjett, sailing, segling, checklistor}
}

% Set up the path to your TTF font here
\newfontfamily{\lcdfon}{LCD-N___.TTF}
\newfontfamily{\lcdfontt}{LCDM2B__.TTF}
\newfontfamily{\lcdfonttt}{DS-Digital}
\newfontfamily{\lcdfont}{TB-Digital-Italic.ttf}
% Define the macro for the LCD display
\newcommand{\lcddd}[1]{%
    \begin{tikzpicture}
      \node[draw,
        minimum width=3.5em,
        minimum height=1em,
        rectangle,
        fill=black!0,
        inner sep=2pt]
      (lcd) {\large\textcolor{black}{\lcdfont #1}};
    \end{tikzpicture}%
}
% Define the macro for the LCD display
% #1 = main text, #2 = bottom right corner text, #3 = background color
\newcommand{\lcd}[1]{%
    \begin{tikzpicture}[baseline=-\the\dimexpr\fontdimen22\textfont2\relax]
        \node[draw, rectangle, minimum width=3.5em, minimum height=1.0em, inner sep=2pt] (lcd) {};
        \node[above right, inner sep=2pt] at (lcd.south west) {\lcdfont #1};
        %% \node[above left, inner sep=1pt, font=\tiny\sffamily] at (lcd.south east) {nm};
    \end{tikzpicture}%
}
\newcommand{\lcdd}[2]{%
    \begin{tikzpicture}[baseline=-\the\dimexpr\fontdimen22\textfont2\relax]
        \node[draw, rectangle, minimum width=3.5em, minimum height=1.0em, inner sep=2pt] (lcd) {};
        \node[above right, inner sep=2pt] at (lcd.south west) {\lcdfont #1};
        \node[above left, inner sep=1pt, font=\tiny\sffamily] at (lcd.south east) {#2};
    \end{tikzpicture}%
}

% Button graphics
\newcommand{\abtn}[1]{
  \begin{tikzpicture}[baseline=-\the\dimexpr\fontdimen22\textfont2\relax]
    \node[draw, minimum width=2em, minimum height=1.2em, rounded corners=1mm] (button) at (0,0) {};
    \node at (button.center) {\scriptsize\textcolor{black}{#1}};
  \end{tikzpicture}
}


\begin{document}
\title{Autopilot Autohelm}
\author{Thomas Beckman}
\date{\today}
\pagestyle{empty}
{\Large Autohelm ST4000\par}
{\textsc{\today} \par}


\section{Course Hold}
\begin{tabular}{p{7cm}}
  \abtn{Auto} \dotfill \lcd{A012o} \\
  'A' indikerar Autopilot på\\
  \abtn{S.By} \dotfill \lcd{C012o} \\
  'C' indikerar att nuvarande kurs visas\\
  Håll inne \abtn{Auto} i 1s, föregående kurs blinkar, tryck \abtn{Auto} igen för att välja\\
\end{tabular}

\section{Track Control}
\begin{tabular}{p{7cm}}
  I läge auto tryck \abtn{+10} + \abtn{--10} för att byta till följning av rutt\\
  \lcdd{1.35}{w.pt} \dotfill avstånd till wpt \\
  \lcdd{t2.23}{nm} \dotfill x-track error \\
  Tryck \abtn{+10} + \abtn{--10} igen för att återgå\\
\end{tabular}

\section{Deadband Mode}
\begin{tabular}{p{7cm}}
  I läge auto tryck \abtn{+1} + \abtn{--1} för att toggla mellan automatiskt och minimalt dödband. Gradsymbolen blinker vid minimalt dödband.\\
\end{tabular}

\section{Windvane Mode}
\begin{tabular}{p{7cm}}
  Tryck \abtn{Auto} + \abtn{S.By} för att följa vindvinkel \dotfill \lcd{w258o}\\
  Håll inne \abtn{Auto} + \abtn{S.By} i 1s för att välja föregående vinkel\\
  Kontrollern kommer pipa var 30s i detta läge
\end{tabular}

\section{Slag}
\begin{tabular}{p{7cm}}
  Tryck \abtn{+1} + \abtn{+10} för att slå 100 grader åt styrbord\\
  Tryck \abtn{--1} + \abtn{--10} för att slå 100 grader åt babord\\
\end{tabular}

\section{Felkoder}
\begin{tabular}{p{7cm}}
  \lcdd{t- - -}{nm}\dotfill missing NMEA0183\\
  \lcdd{tE<<}{nm}\dotfill low GPS signal\\
  \lcd{t0.31}\dotfill too large x-track error\\
\end{tabular}

\end{document}  
